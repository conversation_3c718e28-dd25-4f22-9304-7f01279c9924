<template>
  <div class="login-container">
    <img src="@/assets/image/ci.svg" alt="Logo" class="logo" style="margin: 30px 0 30px 0;">
    <form @submit.prevent="handleLogin">
      <div class="form-group">
        <label for="email">이메일:</label>
        <input type="text" id="email" v-model="userEmail" required :class="{ 'invalid': emailError && userEmail !== 'admin' }" @input="validateEmail" />
        <p v-if="emailError && userEmail !== 'admin'" class="error-message">
          유효한 이메일 주소를 입력해주세요.
        </p>
      </div>
      <div class="form-group">
        <label for="password">비밀번호:</label>
        <input type="password" id="password" v-model="password" required />
      </div>
      <button type="submit" :disabled="isLoading">
        <span v-if="isLoading" class="loading-spinner"></span>
        {{ isLoading ? '로그인 중...' : '로그인' }}
      </button>
      <p v-if="authStore.loginError" class="error-message">
        {{ authStore.loginError }}
      </p>
    </form>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useRouter } from 'vue-router';

// 이메일 유효성 검사를 위한 정규식
const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

const userEmail = ref('');
const password = ref('');
const emailError = ref(false);
const isLoading = ref(false); // 로그인 페이지 내부 로딩 상태
const authStore = useAuthStore();
const router = useRouter();

// 이메일 유효성 검사 함수
const validateEmail = () => {
  // 'admin'인 경우 항상 유효함
  if (userEmail.value === 'admin') {
    emailError.value = false;
    return true;
  }

  // 그 외의 경우 이메일 형식 검사
  emailError.value = !emailRegex.test(userEmail.value);
  return !emailError.value;
};

// 로그인 페이지 로딩 시 입력 필드에 포커스
onMounted(() => {
  // 이메일 필드에 포커스
  setTimeout(() => {
    document.getElementById('email')?.focus();
  }, 100);
});

const handleLogin = async () => {
  // 폼 제출 전 이메일 유효성 검사 (admin이 아닌 경우에만)
  if (userEmail.value !== 'admin' && !validateEmail()) {
    return; // 유효하지 않은 이메일이면 제출 중단
  }

  // 로그인 페이지 내부 로딩 상태 시작
  isLoading.value = true;

  try {
    const result = await authStore.login(userEmail.value, password.value);

    if (result.success) {
      // 로그인 상태에 따라 다른 페이지로 리다이렉션
      if (result.status === 'FORCE_PASSWORD_CHANGE') {
        // 이메일이 응답에 있는지 확인하고 없으면 입력한 값 사용
        if (!authStore.tempUserEmail) {
          authStore.tempUserEmail = userEmail.value;
        }

        router.push('/force-password-change');
      } 
    } 
  } catch (error) {
    console.error('Login error:', error);
  } finally {
    // 로그인 페이지 내부 로딩 상태 종료
    isLoading.value = false;
  }
};
</script>

<style scoped>
.login-container {
  max-width: 400px;
  margin: 50px auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
  text-align: center;
  animation: fadeIn 0.3s ease-in-out; /* 페이지 로딩 시 페이드인 애니메이션 */
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.form-group {
  margin-bottom: 15px;
  text-align: left;
}

label {
  display: block;
  margin-bottom: 5px;
}

input[type="text"],
input[type="password"] {
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
}

button {
  padding: 10px 15px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  position: relative; /* 로딩 스피너 배치를 위한 설정 */
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

button:not(:disabled):hover {
  background-color: #0056b3;
}

.error-message {
  color: red;
  margin-top: 5px;
  font-size: 0.8rem;
}

.invalid {
  border: 1px solid red;
  background-color: #fff8f8;
}

/* 로딩 스피너 스타일 */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
  vertical-align: middle;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>

<template>
  <div class="empty-menu-container">
    <div class="empty-menu-content">
      <div class="empty-menu-icon">
        <i class="fas fa-list-ul"></i>
      </div>
      <h2 class="empty-menu-title">할당된 메뉴가 없습니다</h2>
      <p class="empty-menu-description">
        현재 계정에 할당된 메뉴가 없습니다.<br>
        관리자에게 메뉴 권한 요청을 문의해주세요.
      </p>
      <div class="empty-menu-actions">
        <button @click="refreshPage" class="refresh-btn">
          <i class="fas fa-sync-alt"></i>
          새로고침
        </button>
        <button @click="logout" class="logout-btn">
          <i class="fas fa-sign-out-alt"></i>
          로그아웃
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';

const router = useRouter();
const authStore = useAuthStore();

// 페이지 새로고침
const refreshPage = () => {
  window.location.reload();
};

// 로그아웃
const logout = async () => {
  try {
    await authStore.logout();
    router.push('/login');
  } catch (error) {
    console.error('로그아웃 중 오류가 발생했습니다:', error);
  }
};
</script>

<style scoped>
.empty-menu-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 70vh;
  padding: 20px;
}

.empty-menu-content {
  text-align: center;
  max-width: 500px;
  padding: 40px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.empty-menu-icon {
  margin-bottom: 24px;
}

.empty-menu-icon i {
  font-size: 4rem;
  color: #9e9e9e;
}

.empty-menu-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.empty-menu-description {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 32px;
}

.empty-menu-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.refresh-btn,
.logout-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresh-btn {
  background-color: #2196f3;
  color: white;
}

.refresh-btn:hover {
  background-color: #1976d2;
  transform: translateY(-1px);
}

.logout-btn {
  background-color: #f44336;
  color: white;
}

.logout-btn:hover {
  background-color: #d32f2f;
  transform: translateY(-1px);
}

.refresh-btn:active,
.logout-btn:active {
  transform: translateY(0);
}

/* 반응형 디자인 */
@media (max-width: 768px) {
  .empty-menu-content {
    padding: 30px 20px;
    margin: 0 10px;
  }
  
  .empty-menu-icon i {
    font-size: 3rem;
  }
  
  .empty-menu-title {
    font-size: 1.3rem;
  }
  
  .empty-menu-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .refresh-btn,
  .logout-btn {
    width: 100%;
    max-width: 200px;
  }
}
</style>
